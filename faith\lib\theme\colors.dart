import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

/// 主题颜色枚举
/// 定义应用支持的所有主题颜色
enum AppThemeColor {
  /// 蓝色主题
  blue('蓝色', Color(0xFF3B82F6)),

  /// 绿色主题
  green('绿色', Color(0xFF10B981)),

  /// 紫色主题
  purple('紫色', Color(0xFF8B5CF6)),

  /// 橙色主题
  orange('橙色', Color(0xFFF97316)),

  /// 红色主题
  red('红色', Color(0xFFEF4444)),

  /// 青色主题
  teal('青色', Color(0xFF14B8A6)),

  /// 粉色主题
  pink('粉色', Color(0xFFEC4899)),

  /// 靛蓝主题
  indigo('靛蓝', Color(0xFF6366F1));

  const AppThemeColor(this.displayName, this.seedColor);

  /// 主题显示名称
  final String displayName;

  /// 主题种子颜色
  final Color seedColor;
}

/// 自定义 ShadcnUI 颜色方案
/// 为每个主题颜色提供完全自定义的颜色配置
class CustomShadColorScheme extends ShadColorScheme {
  const CustomShadColorScheme({
    required super.background,
    required super.foreground,
    required super.card,
    required super.cardForeground,
    required super.popover,
    required super.popoverForeground,
    required super.primary,
    required super.primaryForeground,
    required super.secondary,
    required super.secondaryForeground,
    required super.muted,
    required super.mutedForeground,
    required super.accent,
    required super.accentForeground,
    required super.destructive,
    required super.destructiveForeground,
    required super.border,
    required super.input,
    required super.ring,
    required super.selection,
  });
}

/// 自定义颜色方案工厂
/// 根据主题颜色和亮暗模式生成完全自定义的颜色方案
class CustomColorSchemeFactory {
  /// 获取亮色模式的自定义颜色方案
  static ShadColorScheme getLightColorScheme(AppThemeColor themeColor) {
    switch (themeColor) {
      case AppThemeColor.blue:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF0F172A),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF0F172A),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF0F172A),
          primary: Color(0xFF3B82F6),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFF1F5F9),
          secondaryForeground: Color(0xFF0F172A),
          muted: Color(0xFFF1F5F9),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFDBEAFE),
          accentForeground: Color(0xFF0F172A),
          destructive: Color(0xFFEF4444),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFF3B82F6),
          selection: Color(0xFFDBEAFE),
        );

      case AppThemeColor.green:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF0F172A),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF0F172A),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF0F172A),
          primary: Color(0xFF10B981),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFF0FDF4),
          secondaryForeground: Color(0xFF0F172A),
          muted: Color(0xFFF0FDF4),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFD1FAE5),
          accentForeground: Color(0xFF0F172A),
          destructive: Color(0xFFEF4444),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFF10B981),
          selection: Color(0xFFD1FAE5),
        );

      case AppThemeColor.purple:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF0F172A),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF0F172A),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF0F172A),
          primary: Color(0xFF8B5CF6),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFFAF5FF),
          secondaryForeground: Color(0xFF0F172A),
          muted: Color(0xFFFAF5FF),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFEDE9FE),
          accentForeground: Color(0xFF0F172A),
          destructive: Color(0xFFEF4444),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFF8B5CF6),
          selection: Color(0xFFEDE9FE),
        );

      case AppThemeColor.orange:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF0F172A),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF0F172A),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF0F172A),
          primary: Color(0xFFF97316),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFFFF7ED),
          secondaryForeground: Color(0xFF0F172A),
          muted: Color(0xFFFFF7ED),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFFED7AA),
          accentForeground: Color(0xFF0F172A),
          destructive: Color(0xFFEF4444),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFFF97316),
          selection: Color(0xFFFED7AA),
        );

      case AppThemeColor.red:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF0F172A),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF0F172A),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF0F172A),
          primary: Color(0xFFEF4444),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFFEF2F2),
          secondaryForeground: Color(0xFF0F172A),
          muted: Color(0xFFFEF2F2),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFFECDD3),
          accentForeground: Color(0xFF0F172A),
          destructive: Color(0xFFDC2626),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFFEF4444),
          selection: Color(0xFFFECDD3),
        );

      case AppThemeColor.teal:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF0F172A),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF0F172A),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF0F172A),
          primary: Color(0xFF14B8A6),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFF0FDFA),
          secondaryForeground: Color(0xFF0F172A),
          muted: Color(0xFFF0FDFA),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFCCFBF1),
          accentForeground: Color(0xFF0F172A),
          destructive: Color(0xFFEF4444),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFF14B8A6),
          selection: Color(0xFFCCFBF1),
        );

      case AppThemeColor.pink:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF0F172A),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF0F172A),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF0F172A),
          primary: Color(0xFFEC4899),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFFDF2F8),
          secondaryForeground: Color(0xFF0F172A),
          muted: Color(0xFFFDF2F8),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFFCE7F3),
          accentForeground: Color(0xFF0F172A),
          destructive: Color(0xFFEF4444),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFFEC4899),
          selection: Color(0xFFFCE7F3),
        );

      case AppThemeColor.indigo:
        return const CustomShadColorScheme(
          background: Color(0xFFFFFFFF),
          foreground: Color(0xFF1E1B4B),
          card: Color(0xFFFFFFFF),
          cardForeground: Color(0xFF1E1B4B),
          popover: Color(0xFFFFFFFF),
          popoverForeground: Color(0xFF1E1B4B),
          primary: Color(0xFF6366F1),
          primaryForeground: Color(0xFFFFFFFF),
          secondary: Color(0xFFF1F5F9),
          secondaryForeground: Color(0xFF1E1B4B),
          muted: Color(0xFFF1F5F9),
          mutedForeground: Color(0xFF64748B),
          accent: Color(0xFFE0E7FF),
          accentForeground: Color(0xFF1E1B4B),
          destructive: Color(0xFFEF4444),
          destructiveForeground: Color(0xFFFFFFFF),
          border: Color(0xFFE2E8F0),
          input: Color(0xFFE2E8F0),
          ring: Color(0xFF6366F1),
          selection: Color(0xFFE0E7FF),
        );
    }
  }

  /// 获取暗色模式的自定义颜色方案
  static ShadColorScheme getDarkColorScheme(AppThemeColor themeColor) {
    switch (themeColor) {
      case AppThemeColor.blue:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFF60A5FA),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFF60A5FA),
          selection: Color(0xFF1E40AF),
        );

      case AppThemeColor.green:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFF34D399),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFF34D399),
          selection: Color(0xFF047857),
        );

      case AppThemeColor.purple:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFFA78BFA),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFFA78BFA),
          selection: Color(0xFF6D28D9),
        );

      case AppThemeColor.orange:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFFFB923C),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFFFB923C),
          selection: Color(0xFFEA580C),
        );

      case AppThemeColor.red:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFFF87171),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFFF87171),
          selection: Color(0xFFDC2626),
        );

      case AppThemeColor.teal:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFF5EEAD4),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFF5EEAD4),
          selection: Color(0xFF0F766E),
        );

      case AppThemeColor.pink:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFFF472B6),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFFF472B6),
          selection: Color(0xFFDB2777),
        );

      case AppThemeColor.indigo:
        return const CustomShadColorScheme(
          background: Color(0xFF0A0E1A),
          foreground: Color(0xFFF1F5F9),
          card: Color(0xFF1E293B),
          cardForeground: Color(0xFFF1F5F9),
          popover: Color(0xFF1E293B),
          popoverForeground: Color(0xFFF1F5F9),
          primary: Color(0xFF818CF8),
          primaryForeground: Color(0xFF0F172A),
          secondary: Color(0xFF334155),
          secondaryForeground: Color(0xFFCBD5E1),
          muted: Color(0xFF334155),
          mutedForeground: Color(0xFF94A3B8),
          accent: Color(0xFF475569),
          accentForeground: Color(0xFFF1F5F9),
          destructive: Color(0xFFF87171),
          destructiveForeground: Color(0xFF0F172A),
          border: Color(0xFF475569),
          input: Color(0xFF334155),
          ring: Color(0xFF818CF8),
          selection: Color(0xFF4338CA),
        );
    }
  }
}

/// 自定义颜色方案扩展
/// 为现有颜色方案添加额外的颜色属性
extension ShadColorSchemeExtension on ShadColorScheme {
  /// 成功色 - 绿色
  Color get success => const Color(0xFF10B981);

  /// 警告色 - 黄色
  Color get warning => const Color(0xFFF59E0B);

  /// 信息色 - 蓝色
  Color get info => const Color(0xFF3B82F6);

  /// 表面变体色
  Color get surfaceVariant => muted;

  /// 轮廓色
  Color get outline => border;

  /// 轮廓变体色（半透明）
  Color get outlineVariant => border.withValues(alpha: 0.5);

  /// 阴影色（半透明前景色）
  Color get shadow => foreground.withValues(alpha: 0.1);

  /// 表面色调色
  Color get surfaceTint => primary;

  /// 错误色（与 destructive 相同）
  Color get error => destructive;

  /// 错误前景色
  Color get onError => destructiveForeground;
}
