[{"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/animator/fragment_open_enter.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-fragment-1.7.1-4:/animator/fragment_open_enter.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/animator/fragment_close_exit.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-fragment-1.7.1-4:/animator/fragment_close_exit.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/animator/fragment_fade_enter.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-fragment-1.7.1-4:/animator/fragment_fade_enter.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/animator/fragment_open_exit.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-fragment-1.7.1-4:/animator/fragment_open_exit.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/animator/fragment_close_enter.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-fragment-1.7.1-4:/animator/fragment_close_enter.xml"}, {"merged": "io.flutter.plugins.pathprovider.path_provider_android-release-25:/animator/fragment_fade_exit.xml", "source": "io.flutter.plugins.pathprovider.path_provider_android-fragment-1.7.1-4:/animator/fragment_fade_exit.xml"}]